�f�5            �f�5            �f�5            }�"�           
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (ǃ��10{ů�X          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther (ǃ��10���T           
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (ǃ��10�3W��           
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (ǃ��10�7�`           
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (ǃ��10<�Y��          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (ǃ��10�W�6          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (ǃ��10�A�a          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (ǃ��10��W	          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (ǃ��10���� 
          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (ǃ��10L��(1           	39_config
��؈��O�ԓ ǃ��1
�F~           	39_configf
��؈��O�ԓ ǃ��1
����Ą���ԓ ǃ��1
�����ٝ���ԓ ǃ��1
�����ؿ���ԓ ǃ��1�� 
          	39_config�
��؈��O�ԓ ǃ��1
����Ą���ԓ ǃ��1
�����ٝ���ԓ ǃ��1
�����ؿ���ԓ ǃ��1
�ހ���`�ԓ ǃ��1
"�������d�ԓ ǃ��1(���ʖ����L����           	39_config�
��؈��O�ԓ ǃ��1
����Ą���ԓ ǃ��1
�����ٝ���ԓ ǃ��1
�����ؿ���ԓ ǃ��1
�ހ���`�ԓ ǃ��1
"�������d�ԓ ǃ��1(���ʖ����
ۯ��Њ���ԓ ǃ��1��I;          	39_config�
��؈��O�ԓ ǃ��1
����Ą���ԓ ǃ��1
�����ٝ���ԓ ǃ��1
�����ؿ���ԓ ǃ��1
�ހ���`�ԓ ǃ��1
"�������d�ԓ ǃ��1(���ʖ����
ۯ��Њ���ԓ ǃ��1
��՛�����ԓ ǃ��1
���Åօ�C�ԓ ǃ��1
�����Ӆ���ԓ ǃ��1
��������_�ԓ ǃ��1]�~�          	39_config�
��؈��O�ԓ ǃ��1
����Ą���ԓ ǃ��1
�����ٝ���ԓ ǃ��1
�����ؿ���ԓ ǃ��1
�ހ���`�ԓ ǃ��1
"�������d�ԓ ǃ��1(���ʖ����
ۯ��Њ���ԓ ǃ��1
��՛�����ԓ ǃ��1
���Åօ�C�ԓ ǃ��1
�����Ӆ���ԓ ǃ��1
��������_�ԓ ǃ��1
�����������I ǃ��1
�������I ǃ��1
ʒ���қ�C��I ǃ��1
���޾���,��I ǃ��1
���������I ǃ��1�i�>P          	39_config�
��؈��O�ԓ ǃ��1
����Ą���ԓ ǃ��1
�����ٝ���ԓ ǃ��1
�����ؿ���ԓ ǃ��1
�ހ���`�ԓ ǃ��1
"�������d�ԓ ǃ��1(���ʖ����
ۯ��Њ���ԓ ǃ��1
��՛�����ԓ ǃ��1
���Åօ�C�ԓ ǃ��1
�����Ӆ���ԓ ǃ��1
��������_�ԓ ǃ��1
�����������I ǃ��1
�������I ǃ��1
ʒ���қ�C��I ǃ��1
���޾���,��I ǃ��1
���������I ǃ��1
������t�ԓ ǃ��1
��������k�ԓ ǃ��1
գ��������ԓ ǃ��1
��ר�ٳ���ԓ ǃ��1
ෛ�������ԓ ǃ��1
������Ʉ��ԓ ǃ��1�_���          	39_config�
��؈��O�ԓ ǃ��1
����Ą���ԓ ǃ��1
�����ٝ���ԓ ǃ��1
�����ؿ���ԓ ǃ��1
�ހ���`�ԓ ǃ��1
"�������d�ԓ ǃ��1(���ʖ����
ۯ��Њ���ԓ ǃ��1
��՛�����ԓ ǃ��1
���Åօ�C�ԓ ǃ��1
�����Ӆ���ԓ ǃ��1
��������_�ԓ ǃ��1
�����������I ǃ��1
�������I ǃ��1
ʒ���қ�C��I ǃ��1
���޾���,��I ǃ��1
���������I ǃ��1
������t�ԓ ǃ��1
��������k�ԓ ǃ��1
գ��������ԓ ǃ��1
��ר�ٳ���ԓ ǃ��1
ෛ�������ԓ ǃ��1
������Ʉ��ԓ ǃ��1
"��ї�Z�ԓ ǃ��1(Ȏ�������
#�򖐩�����ԓ ǃ��1(Ȏ�������
#�ɕԺ����ԓ ǃ��1(Ȏ�������